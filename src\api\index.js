import axios from 'axios'

export const dataApi = {
  getAllTables() {
    return axios.post('http://localhost:8080/api/dataease/datasets',{
      busiFlag:"dataset"
    })
  },
  getDatasetDetail(datasetId, needAllData = false, customHeaders = {}) {
    console.log('调用getDatasetDetail, ID:', datasetId, '参数:', needAllData);
    
    // 默认headers
    const defaultHeaders = {
      'Content-Type': 'application/json',
      'X-DE-TOKEN': localStorage.getItem('de_token') || '',
      'out_auth_platform': 'default'
    };
    
    // 合并默认headers和自定义headers
    const headers = { ...defaultHeaders, ...customHeaders };
    
    // 打印完整请求信息
    console.log('请求URL:', `http://localhost:8080/api/dataease/dataset/detail/${datasetId}`);
    console.log('请求Headers:', headers);
    
    return axios.get(`http://localhost:8080/api/dataease/dataset/detail/${datasetId}`, {
      params: { needAllData },
      headers: headers
    }).catch(error => {
      console.error('获取数据集详情失败:', error);
      throw error;
    });
    },
  getTableSample(tableCode) {
    return axios.get(`/data/tables/${tableCode}/sample`)
  },
  queryByQuestion(tableCode, question) {
    return axios.get('/analytics/query', {
      params: {
        tableCode,
        question
      }
    })
  },
  // 新增：从Redis获取图表数据的API
  getChartDataById(id) {
    return axios.get(`http://localhost:8080/api/chart/data/${id}`)
  }
}

export const getData = async (data) => {
  console.log('调用getData API，请求参数:', data);
  
  // 如果传入的配置本身就包含完整数据，直接返回格式化的结果
  if (data.data) {
    console.log('配置中已包含数据，直接返回');
    return {
      code: 0,
      msg: 'success',
      data: data
    };
  }
  
  // 添加测试模式，如果请求参数包含test-dataset，则返回模拟数据
  if (data.datasetId === 'test-dataset' || data.tableId === 'test-table') {
    console.log('使用测试模式，返回模拟数据');
    return getMockChartData(data.type);
  }
  
  try {
    // 如果是直接传入的完整数据对象（从AI响应中提取的）
    if (data.code === 0 && data.data) {
      console.log('检测到完整API响应格式，直接返回');
      return data;
    }
    
    // 如果包含chartDataId字段，使用专门的API获取数据
    if (data.chartDataId) {
      console.log('检测到chartDataId，从Redis获取数据:', data.chartDataId);
      const response = await dataApi.getChartDataById(data.chartDataId);
      console.log('Redis数据获取结果:', response);
      return response.data;
    }
    
    // 正常API调用
    console.log('发起API请求:', data);
    const response = await axios.post('/api/chartData/getData-1', data);
    console.log('getData API响应结果:', response);
    
    // 检查响应格式
    if (response.data) {
      console.log('API返回数据:', response.data);
      return response.data;
    } else {
      console.error('响应数据为空');
      return { code: -1, msg: '响应数据为空', data: null };
    }
  } catch (error) {
    console.error('getData API调用出错:', error);
    return { code: -1, msg: error.message, data: null };
    }
}

// 生成模拟图表数据，用于测试
function getMockChartData(chartType) {
  const mockData = {
    code: 0,
    msg: 'success',
    data: {
      type: chartType || 'bar',
      data: [
        { field: '类别A', value: 120 },
        { field: '类别B', value: 200 },
        { field: '类别C', value: 150 },
        { field: '类别D', value: 80 },
        { field: '类别E', value: 170 }
      ],
      metrics: [{ name: '销售额' }]
    }
  };
  
  console.log('生成的模拟数据:', mockData);
  return mockData;
}
